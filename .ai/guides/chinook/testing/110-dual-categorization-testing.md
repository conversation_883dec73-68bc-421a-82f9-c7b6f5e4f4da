# Dual Categorization Testing Patterns

## Table of Contents

- [Overview](#overview)
- [Triple Categorization System Testing](#triple-categorization-system-testing)
- [Polymorphic Relationship Testing](#polymorphic-relationship-testing)
- [Category Type Integration Testing](#category-type-integration-testing)
- [Cross-System Query Testing](#cross-system-query-testing)
- [Performance Integration Testing](#performance-integration-testing)
- [Data Consistency Testing](#data-consistency-testing)

## Overview

This guide provides comprehensive testing patterns for the dual categorization system in the Chinook database implementation. The system integrates three categorization approaches: original Genre table, Category system with polymorphic relationships, and Taxonomy system integration.

**Testing Framework**: All examples use Pest PHP with describe/it blocks following modern Laravel 12 patterns.

**Key Integration Points**:
- Genre ↔ Category mapping and synchronization
- Category ↔ Taxonomy system integration
- Polymorphic categorizable relationships
- Cross-system query optimization
- Data consistency across all three systems

## Triple Categorization System Testing

### System Integration Tests

```php
<?php

use App\Models\Genre;
use App\Models\Category;
use App\Models\Track;
use App\Models\Album;
use App\Models\Artist;
use App\Enums\CategoryType;
use Aliziodev\Taxonomy\Models\Taxonomy;
use Illuminate\Foundation\Testing\RefreshDatabase;

describe('Triple Categorization System Integration', function () {
    uses(RefreshDatabase::class);

    beforeEach(function () {
        $this->seedTripleCategorization();
    });

    describe('Cross-System Data Flow', function () {
        it('maintains consistency across all three categorization systems', function () {
            $track = Track::factory()->create(['genre_id' => Genre::first()->id]);
            
            // Verify Genre relationship
            expect($track->genre)->not->toBeNull();
            expect($track->genre->name)->toBe('Rock');
            
            // Verify Category relationship (auto-created from Genre)
            $genreCategory = $track->categories()
                                  ->where('type', CategoryType::GENRE)
                                  ->first();
            expect($genreCategory)->not->toBeNull();
            expect($genreCategory->name)->toBe('Rock');
            
            // Verify Taxonomy relationship
            $taxonomy = $track->taxonomies()->first();
            expect($taxonomy)->not->toBeNull();
            expect($taxonomy->name)->toBe('Rock');
        });

        it('supports independent categorization in each system', function () {
            $track = Track::factory()->create(['genre_id' => Genre::first()->id]);
            
            // Add custom category
            $moodCategory = Category::factory()->create([
                'type' => CategoryType::MOOD,
                'name' => 'Energetic'
            ]);
            $track->categories()->attach($moodCategory);
            
            // Add custom taxonomy
            $themeTaxonomy = Taxonomy::create([
                'name' => 'Adventure',
                'type' => 'theme'
            ]);
            $track->taxonomies()->attach($themeTaxonomy);
            
            // Verify all systems work independently
            expect($track->genre->name)->toBe('Rock');
            expect($track->categories)->toHaveCount(2); // Genre + Mood
            expect($track->taxonomies)->toHaveCount(2); // Genre + Theme
            
            $categories = $track->categories()->pluck('name')->toArray();
            expect($categories)->toContain('Rock', 'Energetic');
            
            $taxonomies = $track->taxonomies()->pluck('name')->toArray();
            expect($taxonomies)->toContain('Rock', 'Adventure');
        });

        it('handles bulk categorization across systems', function () {
            $tracks = Track::factory()->count(10)->create(['genre_id' => Genre::first()->id]);
            $moodCategory = Category::factory()->create(['type' => CategoryType::MOOD]);
            $themeTaxonomy = Taxonomy::create(['name' => 'Epic', 'type' => 'theme']);
            
            // Bulk attach categories and taxonomies
            foreach ($tracks as $track) {
                $track->categories()->attach($moodCategory);
                $track->taxonomies()->attach($themeTaxonomy);
            }
            
            // Verify bulk operations
            $tracksWithMood = Track::whereHas('categories', function ($query) use ($moodCategory) {
                $query->where('categories.id', $moodCategory->id);
            })->count();
            
            $tracksWithTheme = Track::whereHas('taxonomies', function ($query) use ($themeTaxonomy) {
                $query->where('taxonomies.id', $themeTaxonomy->id);
            })->count();
            
            expect($tracksWithMood)->toBe(10);
            expect($tracksWithTheme)->toBe(10);
        });
    });

    describe('Multi-Entity Categorization', function () {
        it('applies categorization to different entity types', function () {
            $artist = Artist::factory()->create();
            $album = Album::factory()->create(['artist_id' => $artist->id]);
            $track = Track::factory()->create([
                'album_id' => $album->id,
                'genre_id' => Genre::first()->id
            ]);
            
            $styleCategory = Category::factory()->create([
                'type' => CategoryType::GENRE,
                'name' => 'Progressive'
            ]);
            
            // Apply same category to different entities
            $artist->categories()->attach($styleCategory);
            $album->categories()->attach($styleCategory);
            $track->categories()->attach($styleCategory);
            
            // Verify cross-entity categorization
            expect($artist->categories)->toHaveCount(1);
            expect($album->categories)->toHaveCount(1);
            expect($track->categories)->toHaveCount(2); // Genre + Style
            
            // Test cross-entity queries
            $progressiveArtists = Artist::whereHas('categories', function ($query) use ($styleCategory) {
                $query->where('categories.id', $styleCategory->id);
            })->count();
            
            $progressiveAlbums = Album::whereHas('categories', function ($query) use ($styleCategory) {
                $query->where('categories.id', $styleCategory->id);
            })->count();
            
            expect($progressiveArtists)->toBe(1);
            expect($progressiveAlbums)->toBe(1);
        });

        it('supports hierarchical categorization across entities', function () {
            $musicRoot = Category::factory()->create([
                'type' => CategoryType::GENRE,
                'name' => 'Music'
            ]);
            
            $rockCategory = Category::factory()->create([
                'type' => CategoryType::GENRE,
                'name' => 'Rock'
            ]);
            
            $hardRockCategory = Category::factory()->create([
                'type' => CategoryType::GENRE,
                'name' => 'Hard Rock'
            ]);
            
            // Build hierarchy
            $rockCategory->makeChildOf($musicRoot);
            $hardRockCategory->makeChildOf($rockCategory);
            
            $artist = Artist::factory()->create();
            $album = Album::factory()->create(['artist_id' => $artist->id]);
            $track = Track::factory()->create(['album_id' => $album->id]);
            
            // Apply hierarchical categories
            $artist->categories()->attach($rockCategory);
            $album->categories()->attach($hardRockCategory);
            $track->categories()->attach($hardRockCategory);
            
            // Test hierarchical queries
            $rockArtists = Artist::whereHas('categories', function ($query) use ($musicRoot) {
                $query->whereIn('categories.id', $musicRoot->descendants()->pluck('id'));
            })->count();
            
            expect($rockArtists)->toBe(1);
        });
    });

    private function seedTripleCategorization(): void
    {
        // Create Genre
        $genre = Genre::create(['id' => 1, 'name' => 'Rock']);
        
        // Create corresponding Category
        $category = Category::create([
            'type' => CategoryType::GENRE,
            'name' => 'Rock',
            'metadata' => ['genre_id' => $genre->id]
        ]);
        
        // Create corresponding Taxonomy
        $taxonomy = Taxonomy::create([
            'name' => 'Rock',
            'type' => 'genre'
        ]);
    }
});
```

## Polymorphic Relationship Testing

### Categorizable Trait Testing

```php
describe('Polymorphic Categorizable Relationships', function () {
    uses(RefreshDatabase::class);

    describe('Categorizable Trait Functionality', function () {
        it('supports polymorphic categorization for all models', function () {
            $artist = Artist::factory()->create();
            $album = Album::factory()->create(['artist_id' => $artist->id]);
            $track = Track::factory()->create(['album_id' => $album->id]);
            
            $category = Category::factory()->create(['type' => CategoryType::MOOD]);
            
            // Test polymorphic attachment
            $artist->categories()->attach($category);
            $album->categories()->attach($category);
            $track->categories()->attach($category);
            
            // Verify polymorphic relationships
            expect($artist->categories)->toHaveCount(1);
            expect($album->categories)->toHaveCount(1);
            expect($track->categories)->toHaveCount(1);
            
            // Verify reverse relationship
            expect($category->artists)->toHaveCount(1);
            expect($category->albums)->toHaveCount(1);
            expect($category->tracks)->toHaveCount(1);
        });

        it('handles category pivot data correctly', function () {
            $track = Track::factory()->create();
            $category = Category::factory()->create(['type' => CategoryType::THEME]);
            
            // Attach with pivot data
            $track->categories()->attach($category, [
                'is_primary' => true,
                'sort_order' => 1,
                'metadata' => ['confidence' => 0.95]
            ]);
            
            $pivotData = $track->categories()->first()->pivot;
            
            expect($pivotData->is_primary)->toBeTrue();
            expect($pivotData->sort_order)->toBe(1);
            expect($pivotData->metadata['confidence'])->toBe(0.95);
        });

        it('supports category scoping and filtering', function () {
            $track = Track::factory()->create();
            
            $genreCategory = Category::factory()->create(['type' => CategoryType::GENRE]);
            $moodCategory = Category::factory()->create(['type' => CategoryType::MOOD]);
            $themeCategory = Category::factory()->create(['type' => CategoryType::THEME]);
            
            $track->categories()->attach([
                $genreCategory->id => ['is_primary' => true],
                $moodCategory->id => ['is_primary' => false],
                $themeCategory->id => ['is_primary' => false]
            ]);
            
            // Test type-based filtering
            $genreCategories = $track->categories()->where('type', CategoryType::GENRE)->get();
            $primaryCategories = $track->categories()->wherePivot('is_primary', true)->get();
            
            expect($genreCategories)->toHaveCount(1);
            expect($primaryCategories)->toHaveCount(1);
            expect($primaryCategories->first()->id)->toBe($genreCategory->id);
        });
    });

    describe('Cross-Model Category Queries', function () {
        it('finds entities by shared categories', function () {
            $sharedCategory = Category::factory()->create(['type' => CategoryType::MOOD]);
            
            $artists = Artist::factory()->count(3)->create();
            $albums = Album::factory()->count(2)->create();
            $tracks = Track::factory()->count(5)->create();
            
            // Attach shared category to some entities
            $artists[0]->categories()->attach($sharedCategory);
            $artists[1]->categories()->attach($sharedCategory);
            $albums[0]->categories()->attach($sharedCategory);
            $tracks[0]->categories()->attach($sharedCategory);
            $tracks[1]->categories()->attach($sharedCategory);
            $tracks[2]->categories()->attach($sharedCategory);
            
            // Query entities by shared category
            $categorizedArtists = Artist::whereHas('categories', function ($query) use ($sharedCategory) {
                $query->where('categories.id', $sharedCategory->id);
            })->count();
            
            $categorizedAlbums = Album::whereHas('categories', function ($query) use ($sharedCategory) {
                $query->where('categories.id', $sharedCategory->id);
            })->count();
            
            $categorizedTracks = Track::whereHas('categories', function ($query) use ($sharedCategory) {
                $query->where('categories.id', $sharedCategory->id);
            })->count();
            
            expect($categorizedArtists)->toBe(2);
            expect($categorizedAlbums)->toBe(1);
            expect($categorizedTracks)->toBe(3);
        });

        it('supports complex multi-entity category queries', function () {
            $rockCategory = Category::factory()->create([
                'type' => CategoryType::GENRE,
                'name' => 'Rock'
            ]);
            
            $energeticCategory = Category::factory()->create([
                'type' => CategoryType::MOOD,
                'name' => 'Energetic'
            ]);
            
            $artist = Artist::factory()->create();
            $album = Album::factory()->create(['artist_id' => $artist->id]);
            $track = Track::factory()->create(['album_id' => $album->id]);
            
            // Apply categories at different levels
            $artist->categories()->attach($rockCategory);
            $album->categories()->attach($rockCategory);
            $track->categories()->attach([$rockCategory->id, $energeticCategory->id]);
            
            // Complex query: Find tracks that are Rock AND Energetic
            $rockEnergeticTracks = Track::whereHas('categories', function ($query) use ($rockCategory) {
                $query->where('categories.id', $rockCategory->id);
            })->whereHas('categories', function ($query) use ($energeticCategory) {
                $query->where('categories.id', $energeticCategory->id);
            })->count();
            
            expect($rockEnergeticTracks)->toBe(1);
        });
    });
});
```

## Category Type Integration Testing

### CategoryType Enum Integration Tests

```php
describe('Category Type Integration', function () {
    uses(RefreshDatabase::class);

    describe('CategoryType Enum Validation', function () {
        it('enforces category type constraints', function () {
            $track = Track::factory()->create();

            // Test all category types
            $genreCategory = Category::factory()->create(['type' => CategoryType::GENRE]);
            $moodCategory = Category::factory()->create(['type' => CategoryType::MOOD]);
            $themeCategory = Category::factory()->create(['type' => CategoryType::THEME]);
            $eraCategory = Category::factory()->create(['type' => CategoryType::ERA]);
            $instrumentCategory = Category::factory()->create(['type' => CategoryType::INSTRUMENT]);
            $languageCategory = Category::factory()->create(['type' => CategoryType::LANGUAGE]);
            $occasionCategory = Category::factory()->create(['type' => CategoryType::OCCASION]);

            $track->categories()->attach([
                $genreCategory->id,
                $moodCategory->id,
                $themeCategory->id,
                $eraCategory->id,
                $instrumentCategory->id,
                $languageCategory->id,
                $occasionCategory->id
            ]);

            // Verify all types are supported
            expect($track->categories)->toHaveCount(7);

            $categoryTypes = $track->categories()->pluck('type')->unique()->toArray();
            expect($categoryTypes)->toContain(
                CategoryType::GENRE->value,
                CategoryType::MOOD->value,
                CategoryType::THEME->value,
                CategoryType::ERA->value,
                CategoryType::INSTRUMENT->value,
                CategoryType::LANGUAGE->value,
                CategoryType::OCCASION->value
            );
        });

        it('supports type-specific category queries', function () {
            $track = Track::factory()->create();

            $categories = [
                Category::factory()->create(['type' => CategoryType::GENRE, 'name' => 'Rock']),
                Category::factory()->create(['type' => CategoryType::MOOD, 'name' => 'Energetic']),
                Category::factory()->create(['type' => CategoryType::THEME, 'name' => 'Adventure']),
                Category::factory()->create(['type' => CategoryType::ERA, 'name' => '1980s']),
            ];

            foreach ($categories as $category) {
                $track->categories()->attach($category);
            }

            // Test type-specific queries
            $genreCategories = $track->categoriesByType(CategoryType::GENRE);
            $moodCategories = $track->categoriesByType(CategoryType::MOOD);
            $themeCategories = $track->categoriesByType(CategoryType::THEME);
            $eraCategories = $track->categoriesByType(CategoryType::ERA);

            expect($genreCategories)->toHaveCount(1);
            expect($moodCategories)->toHaveCount(1);
            expect($themeCategories)->toHaveCount(1);
            expect($eraCategories)->toHaveCount(1);

            expect($genreCategories->first()->name)->toBe('Rock');
            expect($moodCategories->first()->name)->toBe('Energetic');
        });

        it('validates category type hierarchy constraints', function () {
            // Create parent-child relationships within same type
            $musicRoot = Category::factory()->create([
                'type' => CategoryType::GENRE,
                'name' => 'Music'
            ]);

            $rockGenre = Category::factory()->create([
                'type' => CategoryType::GENRE,
                'name' => 'Rock'
            ]);

            $hardRock = Category::factory()->create([
                'type' => CategoryType::GENRE,
                'name' => 'Hard Rock'
            ]);

            // Valid hierarchy within same type
            $result1 = $rockGenre->makeChildOf($musicRoot);
            $result2 = $hardRock->makeChildOf($rockGenre);

            expect($result1)->toBeTrue();
            expect($result2)->toBeTrue();

            // Invalid hierarchy across different types
            $moodCategory = Category::factory()->create([
                'type' => CategoryType::MOOD,
                'name' => 'Happy'
            ]);

            $result3 = $moodCategory->makeChildOf($rockGenre);
            expect($result3)->toBeFalse();
        });
    });
});
```

## Cross-System Query Testing

### Advanced Query Pattern Tests

```php
describe('Cross-System Query Optimization', function () {
    uses(RefreshDatabase::class);

    beforeEach(function () {
        $this->createComplexCategorization();
    });

    describe('Multi-System Query Performance', function () {
        it('optimizes queries across genre, category, and taxonomy systems', function () {
            // Complex query combining all three systems
            $tracks = Track::with(['genre', 'categories', 'taxonomies'])
                          ->whereHas('genre', function ($query) {
                              $query->where('name', 'Rock');
                          })
                          ->whereHas('categories', function ($query) {
                              $query->where('type', CategoryType::MOOD)
                                    ->where('name', 'Energetic');
                          })
                          ->whereHas('taxonomies', function ($query) {
                              $query->where('type', 'theme')
                                    ->where('name', 'Adventure');
                          })
                          ->get();

            expect($tracks->count())->toBeGreaterThan(0);

            foreach ($tracks as $track) {
                expect($track->genre->name)->toBe('Rock');
                expect($track->categories->where('type', CategoryType::MOOD)->first()->name)->toBe('Energetic');
                expect($track->taxonomies->where('type', 'theme')->first()->name)->toBe('Adventure');
            }
        });

        it('supports aggregation queries across systems', function () {
            // Count tracks by genre and mood combination
            $genreMoodCounts = Track::select('genres.name as genre_name', 'categories.name as mood_name')
                                   ->join('genres', 'tracks.genre_id', '=', 'genres.id')
                                   ->join('categorizables', function ($join) {
                                       $join->on('tracks.id', '=', 'categorizables.categorizable_id')
                                            ->where('categorizables.categorizable_type', Track::class);
                                   })
                                   ->join('categories', function ($join) {
                                       $join->on('categorizables.category_id', '=', 'categories.id')
                                            ->where('categories.type', CategoryType::MOOD);
                                   })
                                   ->groupBy('genres.name', 'categories.name')
                                   ->selectRaw('COUNT(*) as track_count')
                                   ->get();

            expect($genreMoodCounts->count())->toBeGreaterThan(0);
            expect($genreMoodCounts->first()->track_count)->toBeGreaterThan(0);
        });
    });

    private function createComplexCategorization(): void
    {
        // Create genres
        $rockGenre = Genre::create(['name' => 'Rock']);
        $jazzGenre = Genre::create(['name' => 'Jazz']);

        // Create categories
        $energeticMood = Category::factory()->create([
            'type' => CategoryType::MOOD,
            'name' => 'Energetic'
        ]);

        $relaxedMood = Category::factory()->create([
            'type' => CategoryType::MOOD,
            'name' => 'Relaxed'
        ]);

        // Create taxonomies
        $adventureTheme = Taxonomy::create(['name' => 'Adventure', 'type' => 'theme']);
        $romanticTheme = Taxonomy::create(['name' => 'Romantic', 'type' => 'theme']);

        // Create tracks with complex categorization
        $rockTracks = Track::factory()->count(5)->create(['genre_id' => $rockGenre->id]);
        $jazzTracks = Track::factory()->count(3)->create(['genre_id' => $jazzGenre->id]);

        foreach ($rockTracks as $track) {
            $track->categories()->attach($energeticMood);
            $track->taxonomies()->attach($adventureTheme);
        }

        foreach ($jazzTracks as $track) {
            $track->categories()->attach($relaxedMood);
            $track->taxonomies()->attach($romanticTheme);
        }
    }
});
```

## Performance Integration Testing

This section covers testing the performance characteristics of the dual categorization system to ensure optimal query performance and resource utilization.

### Query Performance Testing

```php
it('performs efficiently with large datasets', function () {
    // Create large dataset
    $categories = Category::factory()->count(1000)->create();
    $tracks = Track::factory()->count(10000)->create();

    // Attach random categories to tracks
    $tracks->each(function ($track) use ($categories) {
        $track->categories()->attach(
            $categories->random(rand(1, 5))->pluck('id')
        );
    });

    // Test query performance
    $startTime = microtime(true);

    $result = Track::with(['categories', 'taxonomies'])
        ->whereHas('categories', function ($query) {
            $query->where('type', CategoryType::GENRE);
        })
        ->paginate(50);

    $executionTime = microtime(true) - $startTime;

    expect($executionTime)->toBeLessThan(0.5); // Should execute in under 500ms
    expect($result->count())->toBeGreaterThan(0);
});

it('optimizes queries with proper eager loading', function () {
    $tracks = Track::factory()->count(100)->create();

    // Test N+1 query prevention
    DB::enableQueryLog();

    $tracksWithCategories = Track::with(['categories', 'taxonomies'])->get();

    $queries = DB::getQueryLog();

    // Should be 3 queries: tracks, categories, taxonomies
    expect(count($queries))->toBeLessThanOrEqual(3);

    DB::disableQueryLog();
});
```

### Caching Performance Testing

```php
it('caches category queries effectively', function () {
    $track = Track::factory()->create();
    $categories = Category::factory()->count(5)->create();
    $track->categories()->attach($categories);

    // First query - should hit database
    $startTime = microtime(true);
    $result1 = $track->categories()->get();
    $firstQueryTime = microtime(true) - $startTime;

    // Second query - should hit cache
    $startTime = microtime(true);
    $result2 = $track->categories()->get();
    $secondQueryTime = microtime(true) - $startTime;

    expect($secondQueryTime)->toBeLessThan($firstQueryTime);
    expect($result1->count())->toBe($result2->count());
});
```

## Data Consistency Testing

This section ensures data integrity and consistency across the dual categorization system, particularly during concurrent operations and data migrations.

### Referential Integrity Testing

```php
it('maintains referential integrity during cascading deletes', function () {
    $track = Track::factory()->create();
    $category = Category::factory()->create();
    $taxonomy = Taxonomy::factory()->create();

    $track->categories()->attach($category);
    $track->taxonomies()->attach($taxonomy);

    // Verify relationships exist
    expect($track->categories)->toHaveCount(1);
    expect($track->taxonomies)->toHaveCount(1);

    // Delete track
    $track->delete();

    // Verify pivot records are cleaned up
    expect(DB::table('categorizables')->where('categorizable_id', $track->id)->count())->toBe(0);
    expect(DB::table('taxonomables')->where('taxonomable_id', $track->id)->count())->toBe(0);

    // Verify categories and taxonomies still exist
    expect(Category::find($category->id))->not->toBeNull();
    expect(Taxonomy::find($taxonomy->id))->not->toBeNull();
});

it('handles concurrent category assignments correctly', function () {
    $track = Track::factory()->create();
    $categories = Category::factory()->count(10)->create();

    // Simulate concurrent operations
    $promises = [];

    foreach ($categories as $category) {
        $promises[] = function () use ($track, $category) {
            $track->categories()->attach($category, [
                'is_primary' => false,
                'sort_order' => rand(1, 100),
                'metadata' => json_encode(['source' => 'concurrent_test'])
            ]);
        };
    }

    // Execute all operations
    foreach ($promises as $promise) {
        $promise();
    }

    // Verify all categories were attached
    expect($track->fresh()->categories)->toHaveCount(10);

    // Verify no duplicate attachments
    $pivotRecords = DB::table('categorizables')
        ->where('categorizable_id', $track->id)
        ->where('categorizable_type', Track::class)
        ->get();

    expect($pivotRecords->count())->toBe(10);
    expect($pivotRecords->pluck('category_id')->unique()->count())->toBe(10);
});
```

### Transaction Testing

```php
it('maintains consistency during failed transactions', function () {
    $track = Track::factory()->create();
    $categories = Category::factory()->count(3)->create();

    try {
        DB::transaction(function () use ($track, $categories) {
            // Attach first two categories successfully
            $track->categories()->attach($categories[0]);
            $track->categories()->attach($categories[1]);

            // Simulate failure on third category
            throw new Exception('Simulated failure');

            $track->categories()->attach($categories[2]);
        });
    } catch (Exception $e) {
        // Expected to fail
    }

    // Verify no categories were attached due to rollback
    expect($track->fresh()->categories)->toHaveCount(0);

    // Verify categories still exist
    expect(Category::count())->toBe(3);
});
```

### Data Migration Consistency Testing

```php
it('maintains data consistency during genre preservation migration', function () {
    // Create original genre data
    $genre = Genre::factory()->create(['name' => 'Jazz']);
    $tracks = Track::factory()->count(10)->create(['genre_id' => $genre->id]);

    // Simulate migration to category system
    $category = Category::create([
        'name' => $genre->name,
        'type' => CategoryType::GENRE,
        'metadata' => ['migrated_from_genre_id' => $genre->id]
    ]);

    // Migrate track relationships
    foreach ($tracks as $track) {
        $track->categories()->attach($category, [
            'is_primary' => true,
            'metadata' => ['migration_source' => 'genre_preservation']
        ]);
    }

    // Verify data consistency
    expect($tracks->fresh()->every(function ($track) use ($genre, $category) {
        return $track->genre_id === $genre->id &&
               $track->categories->contains($category);
    }))->toBeTrue();

    // Verify category metadata
    expect($category->metadata['migrated_from_genre_id'])->toBe($genre->id);
});
```
