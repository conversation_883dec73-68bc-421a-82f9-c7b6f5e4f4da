# Genre Preservation Testing Guide

## Table of Contents

- [Overview](#overview)
- [Genre Preservation Strategy Testing](#genre-preservation-strategy-testing)
- [Backward Compatibility Testing](#backward-compatibility-testing)
- [Migration Testing](#migration-testing)
- [Dual Categorization Testing](#dual-categorization-testing)
- [Performance Testing](#performance-testing)
- [Data Integrity Testing](#data-integrity-testing)

## Overview

This guide provides comprehensive testing strategies for the Genre preservation approach in the Chinook database implementation. The Genre preservation strategy maintains the original `genres` table while integrating with the new Category and Taxonomy systems for enhanced functionality.

**Testing Framework**: All examples use Pest PHP with describe/it blocks following modern Laravel 12 patterns.

**Key Testing Areas**:
- Genre table preservation and backward compatibility
- Category integration with Genre mapping
- Taxonomy system integration
- Data migration validation
- Performance impact assessment

## Genre Preservation Strategy Testing

### Core Genre Preservation Tests

```php
<?php

use App\Models\Genre;
use App\Models\Category;
use App\Models\Track;
use App\Enums\CategoryType;
use Illuminate\Foundation\Testing\RefreshDatabase;

describe('Genre Preservation Strategy', function () {
    uses(RefreshDatabase::class);

    beforeEach(function () {
        $this->seedOriginalGenres();
    });

    describe('Original Genre Table Preservation', function () {
        it('preserves all original genre records', function () {
            // Verify all 25 original genres exist
            expect(Genre::count())->toBe(25);
            
            // Verify specific genres from chinook.sql
            expect(Genre::where('name', 'Rock')->exists())->toBeTrue();
            expect(Genre::where('name', 'Jazz')->exists())->toBeTrue();
            expect(Genre::where('name', 'Metal')->exists())->toBeTrue();
            expect(Genre::where('name', 'Alternative & Punk')->exists())->toBeTrue();
        });

        it('maintains original genre IDs and relationships', function () {
            $rockGenre = Genre::where('name', 'Rock')->first();
            $track = Track::factory()->create(['genre_id' => $rockGenre->id]);
            
            expect($track->genre_id)->toBe($rockGenre->id);
            expect($track->genre->name)->toBe('Rock');
        });

        it('preserves genre table structure', function () {
            $genre = Genre::first();
            
            // Verify original columns exist
            expect($genre->getConnection()->getSchemaBuilder()->hasColumn('genres', 'id'))->toBeTrue();
            expect($genre->getConnection()->getSchemaBuilder()->hasColumn('genres', 'name'))->toBeTrue();
            
            // Verify no unexpected modifications
            $columns = $genre->getConnection()->getSchemaBuilder()->getColumnListing('genres');
            expect($columns)->toContain('id', 'name');
        });
    });

    describe('Genre-Category Mapping', function () {
        it('creates category records for each genre', function () {
            // Run genre-to-category mapping
            $this->artisan('chinook:map-genres-to-categories');
            
            // Verify category creation
            $genreCount = Genre::count();
            $genreCategoryCount = Category::where('type', CategoryType::GENRE)->count();
            
            expect($genreCategoryCount)->toBe($genreCount);
        });

        it('maintains genre-category relationship mapping', function () {
            $this->artisan('chinook:map-genres-to-categories');
            
            $rockGenre = Genre::where('name', 'Rock')->first();
            $rockCategory = Category::where('type', CategoryType::GENRE)
                                  ->where('name', 'Rock')
                                  ->first();
            
            expect($rockCategory)->not->toBeNull();
            expect($rockCategory->metadata['genre_id'])->toBe($rockGenre->id);
        });

        it('preserves genre metadata in categories', function () {
            $this->artisan('chinook:map-genres-to-categories');
            
            $genre = Genre::first();
            $category = Category::where('type', CategoryType::GENRE)
                               ->where('metadata->genre_id', $genre->id)
                               ->first();
            
            expect($category->metadata)->toHaveKey('genre_id');
            expect($category->metadata)->toHaveKey('original_name');
            expect($category->metadata['genre_id'])->toBe($genre->id);
            expect($category->metadata['original_name'])->toBe($genre->name);
        });
    });

    private function seedOriginalGenres(): void
    {
        // Seed the original 25 genres from chinook.sql
        $originalGenres = [
            'Rock', 'Jazz', 'Metal', 'Alternative & Punk', 'Rock And Roll',
            'Blues', 'Latin', 'Reggae', 'Pop', 'Soundtrack',
            'Bossa Nova', 'Easy Listening', 'Heavy Metal', 'R&B/Soul',
            'Electronica/Dance', 'World', 'Hip Hop/Rap', 'Science Fiction',
            'TV Shows', 'Sci Fi & Fantasy', 'Drama', 'Comedy',
            'Alternative', 'Classical', 'Opera'
        ];

        foreach ($originalGenres as $index => $genreName) {
            Genre::create([
                'id' => $index + 1,
                'name' => $genreName
            ]);
        }
    }
});
```

## Backward Compatibility Testing

### Legacy Code Compatibility Tests

```php
describe('Backward Compatibility', function () {
    uses(RefreshDatabase::class);

    beforeEach(function () {
        $this->seedOriginalGenres();
    });

    describe('Existing Track-Genre Relationships', function () {
        it('maintains existing track-genre foreign key relationships', function () {
            $genre = Genre::first();
            $track = Track::factory()->create(['genre_id' => $genre->id]);
            
            // Verify relationship works as before
            expect($track->genre)->not->toBeNull();
            expect($track->genre->id)->toBe($genre->id);
            expect($track->genre_id)->toBe($genre->id);
        });

        it('supports legacy genre queries', function () {
            $rockGenre = Genre::where('name', 'Rock')->first();
            Track::factory()->count(5)->create(['genre_id' => $rockGenre->id]);
            
            // Legacy query patterns should still work
            $rockTracks = Track::where('genre_id', $rockGenre->id)->get();
            expect($rockTracks)->toHaveCount(5);
            
            $tracksWithGenre = Track::with('genre')->get();
            expect($tracksWithGenre->first()->genre)->not->toBeNull();
        });

        it('preserves genre-based filtering and sorting', function () {
            $genres = Genre::take(3)->get();
            foreach ($genres as $genre) {
                Track::factory()->count(2)->create(['genre_id' => $genre->id]);
            }
            
            // Test genre-based filtering
            $firstGenreTracks = Track::whereHas('genre', function ($query) use ($genres) {
                $query->where('name', $genres->first()->name);
            })->get();
            
            expect($firstGenreTracks)->toHaveCount(2);
            
            // Test genre-based sorting
            $sortedTracks = Track::join('genres', 'tracks.genre_id', '=', 'genres.id')
                                ->orderBy('genres.name')
                                ->select('tracks.*')
                                ->get();
            
            expect($sortedTracks)->toHaveCount(6);
        });
    });

    describe('API Compatibility', function () {
        it('maintains genre API endpoints', function () {
            $response = $this->getJson('/api/genres');
            
            $response->assertStatus(200)
                    ->assertJsonStructure([
                        'data' => [
                            '*' => ['id', 'name']
                        ]
                    ]);
        });

        it('supports legacy genre-based track filtering', function () {
            $genre = Genre::first();
            Track::factory()->count(3)->create(['genre_id' => $genre->id]);
            
            $response = $this->getJson("/api/tracks?genre_id={$genre->id}");
            
            $response->assertStatus(200);
            expect($response->json('data'))->toHaveCount(3);
        });
    });
});
```

## Migration Testing

### Genre-to-Category Migration Tests

```php
describe('Genre Migration Testing', function () {
    uses(RefreshDatabase::class);

    beforeEach(function () {
        $this->seedOriginalGenres();
        $this->createTestTracks();
    });

    describe('Migration Process Validation', function () {
        it('migrates genres to categories without data loss', function () {
            $originalGenreCount = Genre::count();
            $originalTrackCount = Track::count();
            
            // Run migration
            $this->artisan('chinook:migrate-genres-to-categories');
            
            // Verify no data loss
            expect(Genre::count())->toBe($originalGenreCount);
            expect(Track::count())->toBe($originalTrackCount);
            expect(Category::where('type', CategoryType::GENRE)->count())->toBe($originalGenreCount);
        });

        it('creates polymorphic relationships during migration', function () {
            Track::factory()->count(5)->create(['genre_id' => Genre::first()->id]);
            
            $this->artisan('chinook:migrate-genres-to-categories');
            
            // Verify polymorphic relationships created
            $track = Track::first();
            $genreCategories = $track->categories()->where('type', CategoryType::GENRE)->get();
            
            expect($genreCategories)->toHaveCount(1);
            expect($genreCategories->first()->name)->toBe($track->genre->name);
        });

        it('validates migration rollback capability', function () {
            $this->artisan('chinook:migrate-genres-to-categories');
            
            // Verify migration completed
            expect(Category::where('type', CategoryType::GENRE)->count())->toBeGreaterThan(0);
            
            // Test rollback
            $this->artisan('chinook:rollback-genre-migration');
            
            // Verify rollback (categories removed, genres preserved)
            expect(Category::where('type', CategoryType::GENRE)->count())->toBe(0);
            expect(Genre::count())->toBeGreaterThan(0);
        });
    });

    private function createTestTracks(): void
    {
        $genres = Genre::take(5)->get();
        foreach ($genres as $genre) {
            Track::factory()->count(3)->create(['genre_id' => $genre->id]);
        }
    }
});
```

## Dual Categorization Testing

### Triple Categorization System Tests

```php
describe('Dual Categorization System', function () {
    uses(RefreshDatabase::class);

    beforeEach(function () {
        $this->seedOriginalGenres();
        $this->artisan('chinook:migrate-genres-to-categories');
    });

    describe('Track Categorization Methods', function () {
        it('supports both genre and category relationships', function () {
            $track = Track::factory()->create(['genre_id' => Genre::first()->id]);
            $customCategory = Category::factory()->create(['type' => CategoryType::MOOD]);

            // Attach custom category
            $track->categories()->attach($customCategory);

            // Verify both relationships work
            expect($track->genre)->not->toBeNull();
            expect($track->categories)->toHaveCount(2); // Genre category + custom category

            $genreCategory = $track->categories()->where('type', CategoryType::GENRE)->first();
            $moodCategory = $track->categories()->where('type', CategoryType::MOOD)->first();

            expect($genreCategory->name)->toBe($track->genre->name);
            expect($moodCategory->id)->toBe($customCategory->id);
        });

        it('maintains category type separation', function () {
            $track = Track::factory()->create(['genre_id' => Genre::first()->id]);

            $moodCategory = Category::factory()->create(['type' => CategoryType::MOOD]);
            $themeCategory = Category::factory()->create(['type' => CategoryType::THEME]);

            $track->categories()->attach([$moodCategory->id, $themeCategory->id]);

            // Test type-specific queries
            $genreCategories = $track->categories()->where('type', CategoryType::GENRE)->get();
            $moodCategories = $track->categories()->where('type', CategoryType::MOOD)->get();
            $themeCategories = $track->categories()->where('type', CategoryType::THEME)->get();

            expect($genreCategories)->toHaveCount(1);
            expect($moodCategories)->toHaveCount(1);
            expect($themeCategories)->toHaveCount(1);
        });

        it('supports complex categorization queries', function () {
            $rockGenre = Genre::where('name', 'Rock')->first();
            $energeticMood = Category::factory()->create([
                'type' => CategoryType::MOOD,
                'name' => 'Energetic'
            ]);

            $tracks = Track::factory()->count(5)->create(['genre_id' => $rockGenre->id]);
            foreach ($tracks as $track) {
                $track->categories()->attach($energeticMood);
            }

            // Complex query: Rock tracks with Energetic mood
            $energeticRockTracks = Track::where('genre_id', $rockGenre->id)
                ->whereHas('categories', function ($query) use ($energeticMood) {
                    $query->where('categories.id', $energeticMood->id);
                })->get();

            expect($energeticRockTracks)->toHaveCount(5);
        });
    });

    describe('Category Hierarchy with Genres', function () {
        it('creates hierarchical genre categories', function () {
            $musicRoot = Category::factory()->create([
                'type' => CategoryType::GENRE,
                'name' => 'Music'
            ]);

            $rockCategory = Category::where('type', CategoryType::GENRE)
                                  ->where('name', 'Rock')
                                  ->first();

            $rockCategory->makeChildOf($musicRoot);

            expect($rockCategory->parent_id)->toBe($musicRoot->id);
            expect($musicRoot->children)->toHaveCount(1);
        });

        it('supports genre subcategorization', function () {
            $rockCategory = Category::where('type', CategoryType::GENRE)
                                  ->where('name', 'Rock')
                                  ->first();

            $hardRock = Category::factory()->create([
                'type' => CategoryType::GENRE,
                'name' => 'Hard Rock'
            ]);

            $hardRock->makeChildOf($rockCategory);

            // Test hierarchical queries
            $rockDescendants = $rockCategory->descendants()->get();
            expect($rockDescendants)->toContain(
                fn($category) => $category->name === 'Hard Rock'
            );
        });
    });
});
```

## Performance Testing

### Genre Preservation Performance Tests

```php
describe('Performance Impact Testing', function () {
    uses(RefreshDatabase::class);

    beforeEach(function () {
        $this->seedOriginalGenres();
        $this->createLargeDataset();
    });

    describe('Query Performance Comparison', function () {
        it('measures genre-only query performance', function () {
            $startTime = microtime(true);

            $rockTracks = Track::whereHas('genre', function ($query) {
                $query->where('name', 'Rock');
            })->get();

            $genreQueryTime = (microtime(true) - $startTime) * 1000;

            expect($genreQueryTime)->toBeLessThan(100); // Under 100ms
            expect($rockTracks->count())->toBeGreaterThan(0);
        });

        it('measures dual categorization query performance', function () {
            $this->artisan('chinook:migrate-genres-to-categories');

            $startTime = microtime(true);

            $rockTracks = Track::whereHas('categories', function ($query) {
                $query->where('type', CategoryType::GENRE)
                      ->where('name', 'Rock');
            })->get();

            $categoryQueryTime = (microtime(true) - $startTime) * 1000;

            expect($categoryQueryTime)->toBeLessThan(150); // Acceptable overhead
            expect($rockTracks->count())->toBeGreaterThan(0);
        });

        it('compares memory usage between approaches', function () {
            $memoryBefore = memory_get_usage();

            // Load tracks with genres
            $tracksWithGenres = Track::with('genre')->take(1000)->get();

            $genreMemoryUsage = memory_get_usage() - $memoryBefore;

            // Reset and test with categories
            unset($tracksWithGenres);
            gc_collect_cycles();

            $this->artisan('chinook:migrate-genres-to-categories');

            $memoryBefore = memory_get_usage();

            $tracksWithCategories = Track::with(['categories' => function ($query) {
                $query->where('type', CategoryType::GENRE);
            }])->take(1000)->get();

            $categoryMemoryUsage = memory_get_usage() - $memoryBefore;

            // Category approach should not use significantly more memory
            expect($categoryMemoryUsage)->toBeLessThan($genreMemoryUsage * 1.5);
        });
    });

    private function createLargeDataset(): void
    {
        $genres = Genre::all();

        foreach ($genres as $genre) {
            Track::factory()->count(100)->create(['genre_id' => $genre->id]);
        }
    }
});
```

## Data Integrity Testing

### Comprehensive Data Integrity Tests

```php
describe('Data Integrity Validation', function () {
    uses(RefreshDatabase::class);

    beforeEach(function () {
        $this->seedOriginalGenres();
    });

    describe('Referential Integrity', function () {
        it('maintains foreign key constraints', function () {
            $genre = Genre::first();
            $track = Track::factory()->create(['genre_id' => $genre->id]);

            // Attempt to delete genre with tracks should fail
            expect(fn() => $genre->delete())
                ->toThrow(\Illuminate\Database\QueryException::class);
        });

        it('validates category-genre mapping integrity', function () {
            $this->artisan('chinook:migrate-genres-to-categories');

            $genre = Genre::first();
            $category = Category::where('type', CategoryType::GENRE)
                               ->where('metadata->genre_id', $genre->id)
                               ->first();

            expect($category)->not->toBeNull();
            expect($category->metadata['genre_id'])->toBe($genre->id);
        });

        it('prevents orphaned category relationships', function () {
            $this->artisan('chinook:migrate-genres-to-categories');

            $track = Track::factory()->create(['genre_id' => Genre::first()->id]);
            $category = $track->categories()->where('type', CategoryType::GENRE)->first();

            // Delete category should clean up relationships
            $category->delete();

            expect($track->fresh()->categories()->where('type', CategoryType::GENRE)->count())->toBe(0);
        });
    });

    describe('Data Consistency Validation', function () {
        it('validates genre-category name consistency', function () {
            $this->artisan('chinook:migrate-genres-to-categories');

            $genres = Genre::all();

            foreach ($genres as $genre) {
                $category = Category::where('type', CategoryType::GENRE)
                                  ->where('metadata->genre_id', $genre->id)
                                  ->first();

                expect($category->name)->toBe($genre->name);
            }
        });

        it('validates unique constraints', function () {
            // Test genre name uniqueness
            expect(fn() => Genre::create(['name' => Genre::first()->name]))
                ->toThrow(\Illuminate\Database\QueryException::class);

            // Test category name uniqueness within type
            $this->artisan('chinook:migrate-genres-to-categories');

            $existingCategory = Category::where('type', CategoryType::GENRE)->first();

            expect(fn() => Category::create([
                'type' => CategoryType::GENRE,
                'name' => $existingCategory->name
            ]))->toThrow(\Illuminate\Database\QueryException::class);
        });
    });
});
```
